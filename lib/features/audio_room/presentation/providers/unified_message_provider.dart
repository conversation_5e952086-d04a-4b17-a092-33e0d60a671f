import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 统一消息数据模型
class UnifiedMessage {
  final RoomMessageModel? roomMessage;
  final ChatMessageModel? chatMessage;
  final int timestamp;
  final bool isRoomMessage;

  UnifiedMessage._({
    this.roomMessage,
    this.chatMessage,
    required this.timestamp,
    required this.isRoomMessage,
  });

  factory UnifiedMessage.fromRoomMessage(RoomMessageModel roomMessage) {
    return UnifiedMessage._(
      roomMessage: roomMessage,
      timestamp: roomMessage.createAt ?? DateTime.now().millisecondsSinceEpoch,
      isRoomMessage: true,
    );
  }

  factory UnifiedMessage.fromChatMessage(ChatMessageModel chatMessage) {
    return UnifiedMessage._(
      chatMessage: chatMessage,
      timestamp: chatMessage.timestamp,
      isRoomMessage: false,
    );
  }

  /// 获取消息的唯一标识符
  String get id {
    if (isRoomMessage) {
      return 'room_${roomMessage?.id ?? timestamp.toString()}';
    } else {
      return 'chat_${chatMessage?.id ?? timestamp.toString()}';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UnifiedMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 统一消息状态管理
/// 作为唯一的消息存储，替代原来的两个独立provider
class UnifiedMessageNotifier extends StateNotifier<List<UnifiedMessage>> {
  UnifiedMessageNotifier() : super([]);

  /// 添加房间消息
  void addRoomMessage(RoomMessageModel message) {
    final unifiedMessage = UnifiedMessage.fromRoomMessage(message);
    _addMessageSorted(unifiedMessage);
  }

  /// 添加聊天消息
  void addChatMessage(ChatMessageModel message) {
    final unifiedMessage = UnifiedMessage.fromChatMessage(message);
    _addMessageSorted(unifiedMessage);
  }

  /// 按时间戳顺序插入消息，保持列表有序
  void _addMessageSorted(UnifiedMessage message) {
    final currentMessages = [...state];

    // 检查是否已存在相同消息（避免重复）
    if (currentMessages.any((m) => m.id == message.id)) {
      return;
    }

    // 找到插入位置（保持时间戳顺序）
    int insertIndex = currentMessages.length;
    for (int i = currentMessages.length - 1; i >= 0; i--) {
      if (currentMessages[i].timestamp <= message.timestamp) {
        insertIndex = i + 1;
        break;
      }
      if (i == 0) {
        insertIndex = 0;
      }
    }

    currentMessages.insert(insertIndex, message);
    state = currentMessages;
  }

  /// 移除消息
  void removeMessage(String messageId) {
    state = state.where((m) => m.id != messageId).toList();
  }

  /// 清空所有消息
  void clear() {
    state = [];
  }

  /// 获取消息总数
  int get messageCount => state.length;

  /// 获取房间消息
  List<UnifiedMessage> get roomMessages =>
      state.where((m) => m.isRoomMessage).toList();

  /// 获取聊天消息
  List<UnifiedMessage> get chatMessages =>
      state.where((m) => !m.isRoomMessage).toList();
}

/// 统一消息Provider
final unifiedMessageProvider =
    StateNotifierProvider<UnifiedMessageNotifier, List<UnifiedMessage>>(
  (ref) => UnifiedMessageNotifier(),
);
