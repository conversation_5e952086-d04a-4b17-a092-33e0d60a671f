import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/chat_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_message_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

/// 统一消息列表组件
/// 同时显示房间动态消息和文字聊天消息
class UnifiedMessageList extends ConsumerStatefulWidget {

  const UnifiedMessageList({
    super.key,
  });

  @override
  ConsumerState<UnifiedMessageList> createState() => _UnifiedMessageListState();
}

class _UnifiedMessageListState extends ConsumerState<UnifiedMessageList> {
  final ItemScrollController _itemScrollController = ItemScrollController();
  final ItemPositionsListener _itemPositionsListener =
      ItemPositionsListener.create();

  /// 是否处于列表底部
  bool _isAtBottom = true;

  @override
  void initState() {
    super.initState();
    // 监听滚动位置变化
    _itemPositionsListener.itemPositions.addListener(_onScrollPositionChanged);
  }

  @override
  void dispose() {
    _itemPositionsListener.itemPositions
        .removeListener(_onScrollPositionChanged);
    super.dispose();
  }

  /// 监听滚动位置变化，判断是否在底部
  void _onScrollPositionChanged() {
    final positions = _itemPositionsListener.itemPositions.value;
    if (positions.isNotEmpty) {
      // 获取当前消息总数
      final roomMessages = ref.read(audioRoomMessageProvider);
      final chatMessages = ref.read(chatMessageProvider);
      final totalMessages = roomMessages.length + chatMessages.length;

      if (totalMessages == 0) return;

      // 检查最后一个消息是否可见
      final lastVisibleIndex =
          positions.map((pos) => pos.index).reduce((a, b) => a > b ? a : b);

      // 检查最后一个消息是否可见，或者倒数第二个消息可见且可见度较高
      bool newIsAtBottom = false;

      if (lastVisibleIndex >= totalMessages - 1) {
        // 最后一个消息可见
        newIsAtBottom = true;
      } else if (lastVisibleIndex >= totalMessages - 2) {
        // 倒数第二个消息可见，检查其可见度
        final lastItemPosition = positions.firstWhere(
          (pos) => pos.index == totalMessages - 2,
          orElse: () => positions.first,
        );
        // 如果倒数第二个消息的可见度超过50%，认为接近底部
        newIsAtBottom = lastItemPosition.itemTrailingEdge > 0.5;
      }

      if (_isAtBottom != newIsAtBottom) {
        setState(() {
          _isAtBottom = newIsAtBottom;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final roomMessages = ref.watch(audioRoomMessageProvider);
    final chatMessages = ref.watch(chatMessageProvider);

    // 合并并排序所有消息
    final allMessages = _mergeAndSortMessages(roomMessages, chatMessages);

    // 监听消息变化，只有在底部时才自动滚动到底部
    ref.listen<List<RoomMessageModel>>(audioRoomMessageProvider,
        (previous, next) {
      if (next.isNotEmpty &&
          previous != null &&
          next.length > previous.length &&
          _isAtBottom) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 添加小延迟确保列表已经更新
          Future.delayed(const Duration(milliseconds: 50), () {
            _scrollToBottom();
          });
        });
      }
    });

    ref.listen<List<ChatMessageModel>>(chatMessageProvider, (previous, next) {
      if (next.isNotEmpty &&
          previous != null &&
          next.length > previous.length &&
          _isAtBottom) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 添加小延迟确保列表已经更新
          Future.delayed(const Duration(milliseconds: 50), () {
            _scrollToBottom();
          });
        });
      }
    });

    return ScrollablePositionedList.separated(
      initialAlignment: 0.0,
      initialScrollIndex: allMessages.length - 1,
      padding: EdgeInsets.zero,
      itemCount: allMessages.length,
      separatorBuilder: (context, index) {
        return 5.verticalSpace;
      },
      itemBuilder: (context, index) {
        final message = allMessages[index];
        return _buildMessageItem(message);
      },
      itemScrollController: _itemScrollController,
      itemPositionsListener: _itemPositionsListener,
    );
  }

  /// 合并并排序所有消息
  List<UnifiedMessage> _mergeAndSortMessages(
    List<RoomMessageModel> roomMessages,
    List<ChatMessageModel> chatMessages,
  ) {
    final List<UnifiedMessage> allMessages = [];

    // 添加房间消息
    for (final roomMessage in roomMessages) {
      allMessages.add(UnifiedMessage.fromRoomMessage(roomMessage));
    }

    // 添加聊天消息
    for (final chatMessage in chatMessages) {
      allMessages.add(UnifiedMessage.fromChatMessage(chatMessage));
    }

    // 按时间戳排序
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    return allMessages;
  }

  /// 构建消息项
  Widget _buildMessageItem(UnifiedMessage message) {
    if (message.isRoomMessage) {
      return _RoomMessageItem(message: message.roomMessage!);
    } else {
      return ChatMessageItem(
        message: message.chatMessage!,
        showAvatar: message.chatMessage!.type == ChatMessageType.text,
        showTimestamp: false, // 统一在这里控制时间戳显示
      );
    }
  }

  /// 滚动到底部
  void _scrollToBottom() {
    final roomMessages = ref.read(audioRoomMessageProvider);
    final chatMessages = ref.read(chatMessageProvider);
    final totalMessages = roomMessages.length + chatMessages.length;

    LogUtils.i(totalMessages.toString(), tag: 'totalMessages');

    if (totalMessages > 0 && _itemScrollController.isAttached) {
      // 使用 jumpTo 避免动画回弹，alignment = 1.0 表示项目顶部对齐到视口底部
      _itemScrollController.jumpTo(
        index: totalMessages - 1, // 滚动到最后一条消息（索引从0开始）
        alignment: 1.0, // 1.0 表示项目顶部对齐到视口底部，让最后一条消息显示在底部
      );
    }
  }
}

/// 统一消息数据模型
class UnifiedMessage {
  final RoomMessageModel? roomMessage;
  final ChatMessageModel? chatMessage;
  final int timestamp;
  final bool isRoomMessage;

  UnifiedMessage._({
    this.roomMessage,
    this.chatMessage,
    required this.timestamp,
    required this.isRoomMessage,
  });

  factory UnifiedMessage.fromRoomMessage(RoomMessageModel roomMessage) {
    return UnifiedMessage._(
      roomMessage: roomMessage,
      timestamp: roomMessage.createAt ?? DateTime.now().millisecondsSinceEpoch,
      isRoomMessage: true,
    );
  }

  factory UnifiedMessage.fromChatMessage(ChatMessageModel chatMessage) {
    return UnifiedMessage._(
      chatMessage: chatMessage,
      timestamp: chatMessage.timestamp,
      isRoomMessage: false,
    );
  }
}

/// 房间消息项组件（从原RoomMessagesList复制）
class _RoomMessageItem extends StatelessWidget {
  final RoomMessageModel message;

  const _RoomMessageItem({required this.message});

  @override
  Widget build(BuildContext context) {
    return _buildMessageContent(context);
  }

  Widget _buildMessageContent(BuildContext context) {
    // Handle text chat messages
    if (message.event == RoomMessageEvent.textChat) {
      return _buildChatMessageWithBackground(context);
    }

    // Handle gift messages
    if (message.event == RoomMessageEvent.giftMessage) {
      return _buildMessageRow(
        context: context,
        messageContent: _buildGiftMessageContent(context),
      );
    } else if (message.event == RoomMessageEvent.followCreator) {
      return _buildMessageRow(
        context: context,
        messageContent: _buildTextSpan(
          context: context,
          actionText: 'followed ${message.targetUser?.firstName ?? 'User'}',
        ),
      );
    } else if (message.event == RoomMessageEvent.followBack) {
      return _buildMessageRow(
        context: context,
        messageContent: _buildTextSpan(
          context: context,
          actionText: 'followed ${message.targetUser?.firstName ?? 'User'}',
        ),
      );
    }

    // Handle other message types based on subEvent
    final subEvent = message.eventSubtype;
    switch (subEvent) {
      case RoomMessageEventSubtype.joinRoom:
        return _buildMessageRow(
          context: context,
          messageContent: _buildTextSpan(
            context: context,
            actionText: 'joined the room',
          ),
          crossAxisAlignment: CrossAxisAlignment.start,
        );
      case RoomMessageEventSubtype.agreeInviteOnMic:
        return _buildMessageRow(
          context: context,
          messageContent: _buildTextSpan(
            context: context,
            actionText: 'is now on the mic',
          ),
        );
      case RoomMessageEventSubtype.dropOnMic:
        return _buildMessageRow(
          context: context,
          messageContent: _buildTextSpan(
            context: context,
            actionText: 'has mic removed',
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  /// 构建带背景的聊天消息
  Widget _buildChatMessageWithBackground(BuildContext context) {
    final sender = message.sender;
    final avatarUrl = sender?.avatarUrl ?? '';
    final username = message.sender?.firstName ?? 'User';
    final content = message.content ?? '';
    final Color messageBackgroundColor =
        context.theme.colorScheme.surface.withValues(alpha: 0.3);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: messageBackgroundColor,
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(8),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(10),
        vertical: AppScreenUtils.setHeight(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AvatarWithFrame(
            avatarUrl: avatarUrl,
            width: 18.w,
            height: 18.w,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: '$username: ',
                    style: context.theme.textTheme.bodySmall?.copyWith(
                      color: context.theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(
                    text: content,
                    style: context.theme.textTheme.bodySmall,
                  ),
                ],
              ),
              style: context.theme.textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  /// Creates a standard message row with avatar and content
  Widget _buildMessageRow({
    required BuildContext context,
    required Widget messageContent,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    final sender = message.sender;
    final avatarUrl = sender?.avatarUrl ?? '';
    final Color messageBackgroundColor =
        context.theme.colorScheme.surface.withValues(alpha: 0.3);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: messageBackgroundColor,
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(8),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(10),
        vertical: AppScreenUtils.setHeight(8),
      ),
      child: Row(
        crossAxisAlignment: crossAxisAlignment,
        children: [
          AvatarWithFrame(
            avatarUrl: avatarUrl,
            width: 18.w,
            height: 18.w,
          ),
          SizedBox(width: 8.w),
          Expanded(child: messageContent),
        ],
      ),
    );
  }

  /// Builds a standard text span with username and action text
  Widget _buildTextSpan({
    required BuildContext context,
    required String actionText,
  }) {
    final username = message.sender?.firstName ?? 'User';

    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: '$username ',
            style: context.theme.textTheme.bodySmall?.copyWith(
              color: context.theme.colorScheme.primary,
            ),
          ),
          TextSpan(text: actionText),
        ],
      ),
      style: context.theme.textTheme.bodySmall,
    );
  }

  /// Builds gift message specific content
  Widget _buildGiftMessageContent(BuildContext context) {
    final username = message.sender?.firstName ?? 'User';
    final gift = message.gift;
    final receivers = message.giftReceives;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: '$username ',
                style: context.theme.textTheme.bodySmall?.copyWith(
                  color: context.theme.colorScheme.primary,
                ),
              ),
              TextSpan(text: 'sent ${gift?.name ?? 'gift'}'),
            ],
          ),
          style: context.theme.textTheme.bodySmall,
        ),
        if (receivers?.isNotEmpty ?? false)
          Text.rich(
            TextSpan(
              text: 'to ',
              children: [
                TextSpan(
                  text: receivers!.join(', '),
                  style: context.theme.textTheme.bodySmall?.copyWith(
                    color: context.theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            style: context.theme.textTheme.bodySmall,
          ),
      ],
    );
  }
}
